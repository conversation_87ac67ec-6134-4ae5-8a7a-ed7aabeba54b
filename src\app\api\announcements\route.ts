// auth middleware wired – creator_id da Supabase user
// Questo file gestisce GET e POST per gli annunci multi-tenant.
// POST ora richiede autenticazione JWT e usa l'id reale dell'utente Supabase come creator_id.

import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { validateAnnouncement } from '@/lib/validators';

import { requireAuth } from '@/middleware/auth';

export async function GET(req: NextRequest) {
  // Supporta sia header che query param per tenant
  const tenantSlug = req.nextUrl.searchParams.get('tenant');
  const tenantIdFromHeader = req.headers.get('x-tenant-id');

  let tenantId: string;

  if (tenantSlug) {
    // Mapping slug → tenant_id
    const TENANT_MAP: Record<string, string> = {
      'ua': '1',
      'it': '2',
    };
    tenantId = TENANT_MAP[tenantSlug] || '1'; // default a 'ua'
  } else if (tenantIdFromHeader) {
    tenantId = tenantIdFromHeader;
  } else {
    return NextResponse.json({ error: 'Tenant not specified (use ?tenant=ua or x-tenant-id header)' }, { status: 400 });
  }

  const rows = await query(
    `SELECT id,
            title,
            announcement_type AS type,
            region,
            location
       FROM announcements
      WHERE tenant_id = $1
      ORDER BY created_at DESC
      LIMIT 20`,
    [tenantId]
  );

  return NextResponse.json(rows);
}

export async function POST(req: NextRequest) {
  // Autenticazione JWT obbligatoria
  const auth = await requireAuth(req);
  if ('error' in auth) return auth.error;
  const creatorId = auth.user.id;

  const tenantId = req.headers.get('x-tenant-id');
  if (!tenantId) return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });

  let body: unknown;
  try {
    body = await req.json();
  } catch {
    return NextResponse.json({ error: 'JSON malformato' }, { status: 400 });
  }

  const result = validateAnnouncement(body);
  if (!result.success) {
    return NextResponse.json({ error: 'Validazione fallita', details: result.error.errors }, { status: 422 });
  }

  const { title, description, type, region, location } = result.data;
  try {
    const insertQuery = `
      INSERT INTO announcements (tenant_id, creator_id, announcement_type, title, description, region, location)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id, title, announcement_type AS type, region, location
    `;
    const values = [tenantId, creatorId, type, title, description ?? null, region ?? null, location ?? null];
    const [row] = await query(insertQuery, values);
    return NextResponse.json(row, { status: 201 });
  } catch {
    return NextResponse.json({ error: 'Errore inserimento annuncio' }, { status: 500 });
  }
}
