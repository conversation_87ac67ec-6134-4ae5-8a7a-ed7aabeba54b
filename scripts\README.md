# 🛠️ Scripts di Gestione AWS RDS

Questa cartella contiene script Bash per automatizzare la gestione del database PostgreSQL su Amazon RDS.

## 📋 Prerequisiti

1. **AWS CLI configurata**:

   ```bash
   aws configure
   # Inserisci: Access Key, Secret Key, Region (eu-central-1), Output format (json)
   ```

2. **Permessi AWS necessari**:

   - `rds:*` (per gestire istanze RDS)
   - `ec2:*` (per gestire Security Groups)

3. **GitHub CLI configurata** (per script secrets):

   ```bash
   gh auth login
   # Segui le istruzioni per autenticarti
   ```

4. **Strumenti richiesti**:
   - `bash` (disponibile su Linux/macOS/WSL)
   - `curl` (per rilevare IP pubblico)
   - `jq` (per parsing JSON - installabile con `apt install jq` o `brew install jq`)
   - `gh` (GitHub CLI - installabile con `apt install gh` o `brew install gh`)

## 🚀 Script Disponibili

### 1. `create_rds.sh` - Creazione Istanza RDS

**Scopo**: Crea una nuova istanza PostgreSQL 15 su AWS RDS con configurazione free-tier.

**Uso**:

```bash
export DB_PASSWORD="TuaPasswordSicura123!"
./scripts/create_rds.sh
```

**Variabili personalizzabili**:

```bash
export DB_INSTANCE_ID="mio-progetto-db"    # Nome istanza (default: rebuildlink-db)
export DB_NAME="mio_database"              # Nome database (default: rebuildlink)
export DB_USER="mio_utente"                # Username master (default: rebuildlink_user)
export AWS_REGION="eu-west-1"              # Regione AWS (default: eu-central-1)
```

**Cosa fa**:

- Rileva il tuo IP pubblico per la sicurezza
- Crea una Security Group che permette accesso solo dal tuo IP
- Provisiona istanza PostgreSQL 15 (db.t3.micro, 20GB, free-tier)
- Configura backup automatici (7 giorni)
- Opzionalmente attende che l'istanza sia pronta

### 2. `check_rds_status.sh` - Controllo Stato

**Scopo**: Monitora lo stato dell'istanza RDS e fornisce informazioni di connessione.

**Uso**:

```bash
./scripts/check_rds_status.sh

# Monitoraggio continuo (ogni 30 secondi)
watch -n 30 './scripts/check_rds_status.sh'
```

**Output tipico**:

```
🔍 Controllo stato dell'istanza RDS: rebuildlink-db

📊 Informazioni istanza:
   Status: available
   Engine: postgres 15.4
   Instance Class: db.t3.micro
   Storage: 20GB
   Database Name: rebuildlink
   Master User: rebuildlink_user
   Endpoint: rebuildlink-db.xyz.eu-central-1.rds.amazonaws.com:5432

✅ Istanza DISPONIBILE!

📝 DATABASE_URL di esempio:
   postgres://rebuildlink_user:<PASSWORD>@rebuildlink-db.xyz.eu-central-1.rds.amazonaws.com:5432/rebuildlink
```

### 3. `delete_rds.sh` - Eliminazione Istanza

**Scopo**: Elimina l'istanza RDS e le risorse associate per evitare costi.

⚠️ **ATTENZIONE**: Operazione irreversibile! Tutti i dati saranno persi.

**Uso**:

```bash
./scripts/delete_rds.sh
# Ti verrà chiesto di digitare 'DELETE' per confermare
```

**Cosa elimina**:

- Istanza RDS e tutti i suoi dati
- Backup automatici associati
- Security Group creata per l'istanza

### 4. `set_github_secrets.sh` - Configurazione GitHub Secrets

**Scopo**: Salva in modo sicuro le variabili d'ambiente nel repository GitHub come Actions Secrets.

**Uso**:

```bash
# Esporta le variabili richieste
export DATABASE_URL="**********************************/db"
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_ANON_KEY="eyJhbGci..."

# Salva i secrets su GitHub
./scripts/set_github_secrets.sh
```

**Prerequisiti specifici**:

- GitHub CLI autenticato: `gh auth login`
- Repository Git collegato a GitHub
- Permessi di scrittura sul repository

**Cosa fa**:

- Valida che tutte le variabili richieste siano esportate
- Identifica automaticamente il repository GitHub corrente
- Salva ogni variabile come secret crittografato
- Verifica che i secrets siano stati creati correttamente

**Variabili gestite**:

- `DATABASE_URL` - Connessione al database PostgreSQL
- `SUPABASE_URL` - URL del progetto Supabase
- `SUPABASE_ANON_KEY` - Chiave pubblica Supabase per client-side

### 5. `run_migrations.sh` - Automigrazioni Database

**Scopo**: Esegue automaticamente tutti i file SQL nella cartella `migrations/` contro il database PostgreSQL.

**Uso**:

```bash
# Esporta DATABASE_URL
export DATABASE_URL="**********************************/db"

# Esegui tutte le migrazioni
./scripts/run_migrations.sh
```

**Prerequisiti specifici**:

- PostgreSQL client installato: `sudo apt install postgresql-client`
- Cartella `migrations/` con file SQL
- DATABASE_URL configurata

**Cosa fa**:

- Valida prerequisiti (psql, DATABASE_URL, cartella migrations)
- Testa connessione al database
- Crea tabella `schema_migrations` per tracking
- Esegue migrazioni in ordine alfabetico
- Salta migrazioni già applicate
- Calcola checksum per integrità
- Esegue ogni migrazione in transazione atomica

**Sistema di tracking**:

- **Tabella `schema_migrations`**: Registra filename, timestamp, checksum
- **Idempotenza**: Migrazioni già applicate vengono saltate
- **Integrità**: Checksum rileva modifiche ai file esistenti
- **Atomicità**: Rollback automatico in caso di errore

**Naming convention**:

```
migrations/
├── 001_initial_schema.sql
├── 002_add_categories.sql
├── 20250109_add_user_bio.sql
└── 20250110_create_indexes.sql
```

### 6. `bootstrap_cli_project.sh` - Bootstrap Completo Progetto

**Scopo**: Automatizza completamente il setup del progetto da zero a produzione cloud con un solo comando.

**Uso**:

```bash
# Esporta variabili richieste
export DATABASE_URL="******************************/db"
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_ANON_KEY="eyJhbGciOi..."

# (Opzionale) Personalizza nomi
export GH_REPO_NAME="my-awesome-app"
export VERCEL_PROJECT_NAME="my-awesome-app"

# Avvia bootstrap completo
./scripts/bootstrap_cli_project.sh
```

**Prerequisiti specifici**:

- Git installato e configurato
- GitHub CLI autenticato: `gh auth login`
- Vercel CLI autenticato: `vercel login`
- Tutte le variabili d'ambiente esportate

**Cosa fa**:

- Valida tutti i prerequisiti e autenticazioni
- Crea repository GitHub (pubblico/privato)
- Configura GitHub Actions secrets
- Crea/collega progetto Vercel
- Configura variabili d'ambiente Vercel (production + preview)
- Configura secrets Vercel per CI/CD
- Attiva pipeline completa automatica

**Risultato finale**:

- Repository GitHub con CI/CD attivo
- Progetto Vercel configurato
- Pipeline automatica da commit a produzione
- Tutti i secrets e variabili sincronizzati

**Vantaggi**:

- **Zero configurazione manuale**: Tutto automatizzato
- **Best practices**: Configurazione professionale
- **Riproducibile**: Stesso setup per ogni progetto
- **Error-proof**: Validazioni e controlli integrati

## 🔧 Personalizzazione

Tutti gli script supportano variabili d'ambiente per la personalizzazione:

```bash
# Esempio: crea istanza per ambiente di staging
export DB_INSTANCE_ID="rebuildlink-staging"
export DB_NAME="rebuildlink_staging"
export DB_USER="staging_user"
export DB_PASSWORD="StagingPassword123!"
./scripts/create_rds.sh
```

## 🐛 Risoluzione Problemi

### Errore: "AWS CLI not configured"

```bash
aws configure
# Inserisci le tue credenziali AWS
```

### Errore: "jq: command not found"

```bash
# Ubuntu/Debian
sudo apt install jq

# macOS
brew install jq

# Windows (WSL)
sudo apt install jq
```

### Errore: "Access Denied"

Verifica che il tuo utente AWS abbia i permessi necessari per RDS ed EC2.

### Istanza bloccata in "creating"

È normale, il processo può richiedere 10-15 minuti. Usa `check_rds_status.sh` per monitorare.

## 💰 Gestione Costi

- **Free Tier**: db.t3.micro con 20GB è gratuito per 12 mesi
- **Backup**: 7 giorni di backup sono inclusi nel free tier
- **Eliminazione**: Usa sempre `delete_rds.sh` quando l'istanza non serve più

## 🔗 Link Utili

- [AWS RDS Free Tier](https://aws.amazon.com/rds/free/)
- [PostgreSQL su RDS](https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/CHAP_PostgreSQL.html)
- [AWS CLI Reference](https://docs.aws.amazon.com/cli/latest/reference/rds/)

---

**Tip**: Salva l'endpoint e le credenziali in un password manager per riferimenti futuri!
