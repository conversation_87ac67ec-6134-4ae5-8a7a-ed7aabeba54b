// 📄 src/app/[slug]/annunci/new/page.tsx
// Form creazione annuncio multi-tenant (Project/Job) con validazione lato client e UX moderna.
// Usa useState, useRouter, useParams. Gestisce errori, loading, e redirect post-submit.
// TODO: collegare a endpoint protetto con JWT (token da auth FE).

"use client";
import { useState } from "react";
import { useRouter, useParams } from "next/navigation";

const regioni = ["Kyiv", "Lviv", "Kharkiv", "Lazio"];
const disponibilita = ["full-time", "part-time", "temporaneo"];

export default function NewAnnouncementPage() {
  const router = useRouter();
  const params = useParams();
  const slug = params.slug as string;

  const [type, setType] = useState<"project" | "job">("project");
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  // Project
  const [region, setRegion] = useState("");
  const [budgetMin, setBudgetMin] = useState("");
  const [budgetMax, setBudgetMax] = useState("");
  // Job
  const [location, setLocation] = useState("");
  const [salaryRange, setSalaryRange] = useState("");
  const [availability, setAvailability] = useState("");

  const [errors, setErrors] = useState<{ [k: string]: string }>({});
  const [loading, setLoading] = useState(false);
  const [submitError, setSubmitError] = useState("");

  function validate() {
    const e: { [k: string]: string } = {};
    if (!title) e.title = "Campo obbligatorio";
    if (!description) e.description = "Campo obbligatorio";
    if (type === "project") {
      if (!region) e.region = "Campo obbligatorio";
      if (!budgetMin) e.budgetMin = "Campo obbligatorio";
      if (!budgetMax) e.budgetMax = "Campo obbligatorio";
      if (budgetMin && budgetMax && Number(budgetMin) > Number(budgetMax)) e.budgetMax = "Deve essere ≥ min";
    } else {
      if (!location) e.location = "Campo obbligatorio";
      if (!salaryRange) e.salaryRange = "Campo obbligatorio";
      if (!availability) e.availability = "Campo obbligatorio";
    }
    return e;
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setSubmitError("");
    const errs = validate();
    setErrors(errs);
    if (Object.keys(errs).length > 0) return;
    setLoading(true);
    type ProjectPayload = {
      type: "project";
      title: string;
      description: string;
      region: string;
      budget_min: number;
      budget_max: number;
    };
    type JobPayload = {
      type: "job";
      title: string;
      description: string;
      location: string;
      salary_range: string;
      availability: string;
    };
    const payload: ProjectPayload | JobPayload =
      type === "project"
        ? {
            type,
            title,
            description,
            region,
            budget_min: Number(budgetMin),
            budget_max: Number(budgetMax)
          }
        : {
            type,
            title,
            description,
            location,
            salary_range: salaryRange,
            availability
          };
    try {
      const token = typeof window !== "undefined" ? localStorage.getItem("jwt") : null;
      const headers: Record<string, string> = { "Content-Type": "application/json" };
      if (token) headers["Authorization"] = `Bearer ${token}`;
      const res = await fetch("/api/announcements", {
        method: "POST",
        headers,
        body: JSON.stringify(payload)
      });
      if (res.ok) {
        router.push("/" + slug);
      } else {
        const data = await res.json();
        setSubmitError(data.error || "Errore generico");
      }
    } catch {
      setSubmitError("Errore di rete");
    } finally {
      setLoading(false);
    }
  }

  return (
    <form onSubmit={handleSubmit} className="max-w-xl mx-auto mt-8 p-6 bg-neutral-900 rounded-lg shadow space-y-6">
      <div className="flex gap-2 mb-4">
        <button
          type="button"
          className={`px-4 py-2 rounded-t bg-neutral-800 border-b-2 ${type === "project" ? "border-blue-500 text-blue-400" : "border-transparent text-neutral-400"}`}
          onClick={() => setType("project")}
          disabled={loading}
        >
          Project
        </button>
        <button
          type="button"
          className={`px-4 py-2 rounded-t bg-neutral-800 border-b-2 ${type === "job" ? "border-blue-500 text-blue-400" : "border-transparent text-neutral-400"}`}
          onClick={() => setType("job")}
          disabled={loading}
        >
          Job
        </button>
      </div>
      <div>
        <label className="block mb-1">Titolo *</label>
        <input
          className={`w-full p-2 rounded bg-neutral-800 border ${errors.title ? "border-red-500" : "border-neutral-700"}`}
          value={title}
          onChange={e => setTitle(e.target.value)}
          disabled={loading}
        />
        {errors.title && <div className="text-red-500 text-sm mt-1">{errors.title}</div>}
      </div>
      <div>
        <label className="block mb-1">Descrizione *</label>
        <textarea
          className={`w-full p-2 rounded bg-neutral-800 border ${errors.description ? "border-red-500" : "border-neutral-700"}`}
          value={description}
          onChange={e => setDescription(e.target.value)}
          disabled={loading}
        />
        {errors.description && <div className="text-red-500 text-sm mt-1">{errors.description}</div>}
      </div>
      {type === "project" ? (
        <>
          <div>
            <label className="block mb-1">Regione *</label>
            <select
              className={`w-full p-2 rounded bg-neutral-800 border ${errors.region ? "border-red-500" : "border-neutral-700"}`}
              value={region}
              onChange={e => setRegion(e.target.value)}
              disabled={loading}
            >
              <option value="">Seleziona regione</option>
              {regioni.map(r => <option key={r} value={r}>{r}</option>)}
            </select>
            {errors.region && <div className="text-red-500 text-sm mt-1">{errors.region}</div>}
          </div>
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="block mb-1">Budget min *</label>
              <input
                type="number"
                className={`w-full p-2 rounded bg-neutral-800 border ${errors.budgetMin ? "border-red-500" : "border-neutral-700"}`}
                value={budgetMin}
                onChange={e => setBudgetMin(e.target.value)}
                disabled={loading}
              />
              {errors.budgetMin && <div className="text-red-500 text-sm mt-1">{errors.budgetMin}</div>}
            </div>
            <div className="flex-1">
              <label className="block mb-1">Budget max *</label>
              <input
                type="number"
                className={`w-full p-2 rounded bg-neutral-800 border ${errors.budgetMax ? "border-red-500" : "border-neutral-700"}`}
                value={budgetMax}
                onChange={e => setBudgetMax(e.target.value)}
                disabled={loading}
              />
              {errors.budgetMax && <div className="text-red-500 text-sm mt-1">{errors.budgetMax}</div>}
            </div>
          </div>
        </>
      ) : (
        <>
          <div>
            <label className="block mb-1">Luogo *</label>
            <input
              className={`w-full p-2 rounded bg-neutral-800 border ${errors.location ? "border-red-500" : "border-neutral-700"}`}
              value={location}
              onChange={e => setLocation(e.target.value)}
              disabled={loading}
            />
            {errors.location && <div className="text-red-500 text-sm mt-1">{errors.location}</div>}
          </div>
          <div>
            <label className="block mb-1">Range stipendio *</label>
            <input
              className={`w-full p-2 rounded bg-neutral-800 border ${errors.salaryRange ? "border-red-500" : "border-neutral-700"}`}
              value={salaryRange}
              onChange={e => setSalaryRange(e.target.value)}
              disabled={loading}
            />
            {errors.salaryRange && <div className="text-red-500 text-sm mt-1">{errors.salaryRange}</div>}
          </div>
          <div>
            <label className="block mb-1">Disponibilità *</label>
            <select
              className={`w-full p-2 rounded bg-neutral-800 border ${errors.availability ? "border-red-500" : "border-neutral-700"}`}
              value={availability}
              onChange={e => setAvailability(e.target.value)}
              disabled={loading}
            >
              <option value="">Seleziona</option>
              {disponibilita.map(d => <option key={d} value={d}>{d}</option>)}
            </select>
            {errors.availability && <div className="text-red-500 text-sm mt-1">{errors.availability}</div>}
          </div>
        </>
      )}
      {submitError && <div className="text-red-500 text-sm mt-2">{submitError}</div>}
      <button
        type="submit"
        className="w-full py-2 rounded bg-blue-600 hover:bg-blue-700 disabled:opacity-60 flex items-center justify-center gap-2"
        disabled={loading}
      >
        {loading && <span className="animate-spin">🔄</span>}
        Salva annuncio
      </button>
    </form>
  );
}
