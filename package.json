{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --runInBand", "db:up": "docker compose up -d db", "db:down": "docker compose down"}, "dependencies": {"@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.50.3", "axios": "^1.10.0", "clsx": "^2.1.1", "next": "15.3.5", "pg": "^8.16.3", "react": "^19.0.0", "react-dom": "^19.0.0", "swr": "^2.3.4", "zod": "^3.25.75"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "dotenv": "^17.2.0", "eslint": "^9", "eslint-config-next": "15.3.5", "jest": "^30.0.4", "js-yaml": "^4.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.4", "ts-jest": "^29.4.0", "typescript": "^5"}}