import { z } from 'zod';

// Valida i dati di un annuncio (solo tipi 'project' e 'job')
export const announcementSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  type: z.enum(['project', 'job']),
  region: z.string().optional(),
  location: z.string().optional(),
});

export function validateAnnouncement(data: unknown) {
  return announcementSchema.safeParse(data);
}
