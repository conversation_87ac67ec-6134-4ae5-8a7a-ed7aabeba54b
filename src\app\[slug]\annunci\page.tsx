// 📄 src/app/[slug]/annunci/page.tsx
// Lista annunci per tenant - mostra gli annunci del tenant corrente
// Utilizza il componente AnnouncementsList e gestisce il routing dinamico

"use client";
import { useParams } from "next/navigation";
import AnnouncementsList from "@/components/AnnouncementsList";

/**
 * Pagina lista annunci per un tenant specifico
 * URL: /[slug]/annunci (es. /ua/annunci, /it/annunci)
 */
export default function AnnouncementsPage() {
  const params = useParams();
  const slug = params.slug as string;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Annunci - {slug.toUpperCase()}
          </h1>
          <p className="text-gray-600">
            Trova opportunità di lavoro e progetti nella tua comunità
          </p>
        </header>

        {/* Lista degli annunci */}
        <AnnouncementsList tenantSlug={slug} />

        {/* Pulsante per creare nuovo annuncio */}
        <div className="mt-8 text-center">
          <a
            href={`/${slug}/annunci/new`}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            + Crea Nuovo Annuncio
          </a>
        </div>
      </div>
    </div>
  );
}
