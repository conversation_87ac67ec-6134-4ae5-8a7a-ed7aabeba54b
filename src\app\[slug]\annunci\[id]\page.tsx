// 📄 src/app/[slug]/annunci/[id]/page.tsx
// Dettaglio annuncio: fetch by id, gestione errori, card info, placeholder chat. UX moderna, robusta.
// TODO: collegare chat e migliorare gestione auth lato client.

"use client";
import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";

export default function AnnouncementDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { slug, id } = params as { slug: string; id: string };

  interface AnnouncementDetail {
    title?: string;
    description?: string;
    type?: string;
    region?: string;
    budget_min?: number;
    budget_max?: number;
    location?: string;
    salary_range?: string;
    availability?: string;
    // Add other fields as needed
  }
  const [data, setData] = useState<AnnouncementDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    let isMounted = true;
    async function fetchData() {
      setLoading(true);
      setError("");
      try {
        const headers: Record<string, string> = {};
        const token = typeof window !== "undefined" ? localStorage.getItem("jwt") : null;
        if (token) headers["Authorization"] = `Bearer ${token}`;
        try {
          const res = await fetch(`/api/announcements/${id}`, { headers });
          if (!res.ok) throw new Error("Annuncio non trovato o errore server");
          const json = await res.json();
          if (isMounted) setData(json);
        } catch (e) {
          console.error("❌ Client fetch error:", e);
          if (isMounted) setError("Network error");
        }
      } finally {
        if (isMounted) setLoading(false);
      }
    }
    fetchData();
    return () => { isMounted = false; };
  }, [id]);

  if (loading) {
    return <div className="flex items-center justify-center h-64"><span className="animate-spin text-3xl">🔄</span></div>;
  }

  if (error) {
    return (
      <div className="max-w-xl mx-auto mt-8">
        <div className="bg-red-100 text-red-700 p-4 rounded mb-4">{error}</div>
        <button onClick={() => router.back()} className="btn btn-primary">Back</button>
      </div>
    );
  }

  if (!data) return null;

  const { title, description, type, region, budget_min, budget_max, location, salary_range, availability } = data;

  return (
    <div className="max-w-xl mx-auto mt-8">
      <button onClick={() => router.push('/' + slug)} className="mb-4 text-blue-500 hover:underline">← Back to list</button>
      <div className="bg-neutral-900 p-6 rounded-lg shadow">
        <h1 className="text-2xl font-bold">{title || "(Senza titolo)"}</h1>
        <p className="mt-2 whitespace-pre-wrap">{description || "(Nessuna descrizione)"}</p>
        {type === "project" ? (
          <div className="mt-4 text-sm text-neutral-300">
            <div>Regione: {region || "-"}</div>
            <div>Budget: €{budget_min ?? "-"} – €{budget_max ?? "-"}</div>
          </div>
        ) : type === "job" ? (
          <div className="mt-4 text-sm text-neutral-300">
            <div>Luogo: {location || "-"}</div>
            <div>Stipendio: {salary_range || "-"}</div>
            <div>Disponibilità: {availability || "-"}</div>
          </div>
        ) : null}
      </div>
      <div className="mt-6">
        <label className="block mb-1 font-semibold">Chat</label>
        <textarea disabled placeholder="In arrivo…" className="w-full h-24 mt-1 border rounded-md opacity-50" />
        <button disabled className="mt-2 btn btn-primary opacity-50">Send</button>
      </div>
    </div>
  );
}
