import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    // Disabilita le nuove funzionalità che richiedono parametri asincroni
    dynamicIO: false,
    // Mantieni compatibilità con parametri sincroni
    typedRoutes: false,
  },
  // Configurazione TypeScript per compatibilità
  typescript: {
    // Durante la build, ignora gli errori di tipo per i parametri
    // Questo è un workaround temporaneo per Next.js 15
    ignoreBuildErrors: false,
  },
};

export default nextConfig;
