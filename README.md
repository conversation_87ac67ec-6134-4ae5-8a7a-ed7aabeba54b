# ReBuild Link

Marketplace multi‑tenant per la ricostruzione in contesti complessi (prima istanza: **Ucraina**). Piattaforma web + API + asset dati.

---

## 🌐 Visione

Un unico hub dove – in qualunque area post‑crisi – aziende internazionali possono:

- trovare fornitori di mezzi, materiali, logistica
- reclutare personale locale/estero (job request)
- accedere a report di mercato basati sui dati generati.

Il codice è **riutilizzabile**: ogni territorio è un _tenant_ isolato tramite `tenant_id`.

---

## 🔧 Stack Tecnologico

| Layer                    | Scelte                                                    |
| ------------------------ | --------------------------------------------------------- |
| **Frontend**             | Next.js 15 (App Router) · React · TypeScript · Tailwind 3 |
| **Backend (API Routes)** | Node (Next API) · pg Pool                                 |
| **Auth / Storage**       | Supabase (GoTrue, Storage)                                |
| **Database**             | PostgreSQL 15 (Docker → AWS RDS in prod)                  |
| **CI/CD**                | GitHub Actions → Vercel (frontend)                        |
| **Dev tools**            | VS Code + Copilot                                         |

---

## 📚 Schema dati essenziale

```
tenants  (id, slug, name, default_locale, ...)
users    (id, tenant_id, user_type, ...)
announcements (id, tenant_id, creator_id, type, ...)
announcement_categories (announcement_id, category_id)
messages, ratings, event_logs ...
```

Multi‑tenancy via colonna `tenant_id` + middleware che legge lo slug URL.

---

## 🛣️ API (MVP)

| Verbo    | Rotta                     | Descrizione                        | Parametri                           |
| -------- | ------------------------- | ---------------------------------- | ----------------------------------- |
| **GET**  | `/api/announcements`      | Lista ultimi 20 annunci del tenant | `?tenant=ua` o header `x-tenant-id` |
| **POST** | `/api/announcements`      | Crea nuovo annuncio _(auth)_       | Header `x-tenant-id` + JWT          |
| **GET**  | `/api/announcements/[id]` | Dettaglio singolo annuncio         | -                                   |
| **POST** | `/api/auth/signup`        | Registrazione Supabase             | `{email, password}`                 |
| **POST** | `/api/auth/login`         | Login Supabase                     | `{email, password}`                 |

**Caratteristiche**:

- Validazione payload tramite `src/lib/validators.ts` (Zod)
- Auth protetta da middleware JWT (`src/middleware/auth.ts`)
- Multi-tenancy via query params (`?tenant=ua`) o header (`x-tenant-id`)
- Mapping automatico slug → tenant_id (`ua` → `1`, `it` → `2`)

---

## 🚀 Avvio locale

```bash
# 1. Clona repo e installa dipendenze
cd app && npm install

# 2. Avvia Postgres in Docker
cd ..
docker run --name rebuild-postgres -e POSTGRES_PASSWORD=pass123 -e POSTGRES_DB=rebuildlink -p 5432:5432 -d postgres:15

# 3. Migrazione schema
psql "postgresql://postgres:pass123@localhost:5432/rebuildlink" -f db/migrations/001_schema.sql

# 4. Seed demo (categorie + annunci)
docker cp db/seed.sql rebuild-postgres:/tmp/seed.sql
docker exec -it rebuild-postgres psql -U postgres -d rebuildlink -f /tmp/seed.sql

# 5. Variabili ambiente
# Crea il file .env.local con le seguenti variabili:
cat > app/.env.local << EOF
DATABASE_URL=postgresql://postgres:pass123@localhost:5432/rebuildlink

# Supabase per client-side (frontend)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Supabase per server-side (API routes)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
EOF

# 6. Dev server
cd app && npm run dev  # http://localhost:3000 (o porta disponibile)
```

---

## 🗂 Architettura cartelle (app/)

```
app/
 ├─ src/
 │   ├─ app/                    # App Router (Next.js 15)
 │   │   ├─ (auth)/signin/      # Gruppo route autenticazione
 │   │   ├─ [slug]/             # Route dinamiche tenant (ua, it)
 │   │   │   ├─ annunci/        # Lista annunci per tenant
 │   │   │   │   ├─ [id]/       # Dettaglio singolo annuncio
 │   │   │   │   └─ new/        # Form nuovo annuncio
 │   │   │   └─ layout.tsx      # Layout specifico tenant
 │   │   ├─ api/                # API Routes server-side
 │   │   │   ├─ auth/           # Endpoint autenticazione
 │   │   │   └─ announcements/  # CRUD annunci
 │   │   ├─ layout.tsx          # Layout globale
 │   │   ├─ page.tsx            # Home (redirect a /signin)
 │   │   └─ globals.css         # Stili Tailwind
 │   ├─ components/             # Componenti React riutilizzabili
 │   │   ├─ LocaleContext.tsx   # Context multilingua (en/ua)
 │   │   ├─ LocaleToggle.tsx    # Toggle lingua header
 │   │   ├─ AnnouncementsList.tsx # Lista annunci con SWR
 │   │   └─ ProtectedPage.tsx   # HOC per route protette
 │   ├─ lib/                    # Utilities e configurazioni
 │   │   ├─ supabaseClient.ts   # Client Supabase frontend
 │   │   ├─ db.ts               # Pool PostgreSQL
 │   │   ├─ validators.ts       # Schemi Zod validazione
 │   │   └─ authHelper.ts       # Helper autenticazione
 │   ├─ middleware/             # Middleware server-side
 │   │   └─ auth.ts             # Middleware JWT per API
 │   ├─ i18n/                   # Internazionalizzazione
 │   │   ├─ en.ts               # Traduzioni inglesi
 │   │   └─ ua.ts               # Traduzioni ucraine
 │   ├─ types/                  # Tipi TypeScript condivisi
 │   └─ test/                   # Test Jest
 ├─ public/                     # Asset statici
 ├─ db/                         # Database schema e seed
 │   └─ seed.sql                # Dati demo
 ├─ .env.local                  # Variabili ambiente (git-ignored)
 ├─ package.json                # Dipendenze e script
 ├─ tsconfig.json               # Configurazione TypeScript
 ├─ tailwind.config.js          # Configurazione Tailwind CSS
 └─ next.config.ts              # Configurazione Next.js 15
```

---

## ✨ Funzionalità Implementate

### 🎯 **Frontend (React + Next.js 15)**

- ✅ **Routing Multi-tenant**: Route dinamiche `/[slug]/annunci` (ua, it)
- ✅ **Autenticazione UI**: Pagina signin con Supabase Auth UI
- ✅ **Internazionalizzazione**: Context React per EN/UA con toggle header
- ✅ **Layout Responsive**: Tailwind CSS con font Google Geist
- ✅ **Lista Annunci**: Componente con SWR per fetch ottimizzato
- ✅ **Form Annunci**: Pagine per creazione e dettaglio annunci

### 🔧 **Backend (API Routes)**

- ✅ **API Multi-tenant**: Supporto query params (`?tenant=ua`) e header
- ✅ **Autenticazione**: Endpoint login/signup con Supabase
- ✅ **CRUD Annunci**: API complete per gestione annunci
- ✅ **Middleware JWT**: Protezione route con validazione token
- ✅ **Validazione Zod**: Schema validation per tutti i payload

### 🏗️ **Architettura**

- ✅ **Next.js 15 App Router**: Struttura moderna con Server Components
- ✅ **TypeScript Strict**: Tipizzazione completa e type safety
- ✅ **Database Pool**: Connessione PostgreSQL ottimizzata
- ✅ **Multi-tenancy**: Isolamento dati via `tenant_id`
- ✅ **Build System**: ESLint + TypeScript + Tailwind integrati

### 🔄 **Migliorie Tecniche Recenti**

- ✅ **Query Params API**: Migrazione da header a query params per pulizia
- ✅ **Mapping Centralizzato**: Slug → tenant_id gestito lato server
- ✅ **Compatibilità Next.js 15**: Parametri asincroni per route dinamiche
- ✅ **Retrocompatibilità**: API supporta sia query params che header
- ✅ **Error Handling**: Gestione errori robusta con logging

---

## 🏁 Roadmap (Q3 2025)

| Fase          | Obiettivo                       | Stato |
| ------------- | ------------------------------- | ----- |
| **MVP 1**     | Home + lista annunci (GET)      | ✅    |
| **MVP 2**     | POST /announcements + Form FE   | ✅    |
| **MVP 3**     | Auth UI + route protette        | ✅    |
| **MVP 4**     | Chat realtime (Supabase)        | 🔜    |
| **CI/CD**     | GitHub Actions + Vercel preview | ◻︎    |
| **Analytics** | Dashboard trend / report export | ◻︎    |

---

## � Comandi di Sviluppo

```bash
# Sviluppo
npm run dev          # Avvia server di sviluppo
npm run build        # Build di produzione
npm run start        # Avvia build di produzione
npm run lint         # Controllo ESLint

# Testing
npm run test         # Esegui test Jest
npm run test:watch   # Test in modalità watch

# Database (Docker)
npm run db:up        # Avvia PostgreSQL container
npm run db:down      # Ferma PostgreSQL container

# Database (AWS RDS)
./scripts/test_scripts.sh    # Testa script e dipendenze
./scripts/create_rds.sh      # Crea istanza RDS PostgreSQL
./scripts/check_rds_status.sh # Controlla stato istanza RDS
./scripts/run_migrations.sh  # Esegui migrazioni database
./scripts/delete_rds.sh      # Elimina istanza RDS (ATTENZIONE!)

# Secrets & Deployment
./scripts/check_env_vars.sh     # Verifica variabili d'ambiente
./scripts/set_github_secrets.sh # Configura GitHub Actions secrets
./scripts/setup_vercel_secrets.sh # Configura secrets Vercel per CI/CD
./scripts/bootstrap_cli_project.sh # Bootstrap completo da zero a cloud
```

### 🔍 **URL di Test**

- **Home**: `http://localhost:3000` → redirect a `/signin`
- **Signin**: `http://localhost:3000/signin`
- **Annunci UA**: `http://localhost:3000/ua/annunci`
- **Annunci IT**: `http://localhost:3000/it/annunci`
- **API Annunci**: `http://localhost:3000/api/announcements?tenant=ua`

---

## �🤝 Contribuire

1. Apri una issue o un thread dedicato (#frontend, #backend, ecc.).
2. Mantieni PR piccole; un task → un commit.
3. **Sempre eseguire prima del push**:
   ```bash
   npm run lint && npm run build
   ```
4. Testa le funzionalità su entrambi i tenant (ua, it).

---

## 🚀 Provisioning del database su AWS

> In questa sezione impariamo a spostare il nostro DB Postgres dal
> laptop al cloud utilizzando Amazon RDS.

### 1. Esegui lo script

```bash
# Scegli una password sicura
export DB_PASSWORD="SuperSegreta123!"
./scripts/create_rds.sh
```

### 2. Cosa fa lo script?

1. **Rileva il tuo IP pubblico** per restringere l'accesso.

2. **Crea (o riutilizza) una Security Group** che apre solo la porta 5432 al tuo IP.

3. **Provisiona un'istanza PostgreSQL 15** free-tier (db.t3.micro, 20 GB) nella regione eu-central-1.

4. **Abilita backup automatici** giornalieri (7 giorni).

Una volta che AWS segna l'istanza come AVAILABLE, copia il suo endpoint
e costruisci la tua DATABASE_URL in formato:

```
postgres://<DB_USER>:<DB_PASSWORD>@<ENDPOINT>:5432/<DB_NAME>
```

💡 **Tip**: Conserva questa variabile per i prossimi step di CI/CD.

### 3. Configurazione ambiente produzione

Dopo aver ottenuto l'endpoint RDS, aggiorna il tuo `.env.local` (o `.env.production`):

```bash
# Database di produzione su AWS RDS
DATABASE_URL=postgres://rebuildlink_user:<EMAIL>:5432/rebuildlink

# Mantieni le altre variabili Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 4. Migrazione schema su RDS

Una volta che l'istanza è pronta, esegui la migrazione:

```bash
# Connettiti al database RDS e crea lo schema
psql "**************************************************************/rebuildlink" -f db/migrations/001_schema.sql

# Opzionale: carica i dati demo
psql "**************************************************************/rebuildlink" -f db/seed.sql
```

### 5. Script di gestione aggiuntivi

```bash
# Controlla lo stato dell'istanza RDS
./scripts/check_rds_status.sh

# Monitora in tempo reale (ogni 30 secondi)
watch -n 30 './scripts/check_rds_status.sh'

# Elimina l'istanza quando non serve più (ATTENZIONE: irreversibile!)
./scripts/delete_rds.sh
```

### 6. Vantaggi del database in cloud

- ✅ **Backup automatici**: Snapshot giornalieri per 7 giorni
- ✅ **Scalabilità**: Possibilità di aumentare storage e compute
- ✅ **Sicurezza**: Accesso limitato al tuo IP + VPC isolata
- ✅ **Monitoring**: CloudWatch metrics integrate
- ✅ **Multi-AZ**: Disponibilità elevata (upgrade futuro)
- ✅ **Gestione semplificata**: Script automatizzati per provisioning e cleanup

---

## 🔑 Configurazione Secrets GitHub & Vercel

> In questa sezione impariamo a gestire in modo sicuro le variabili d'ambiente
> sensibili per GitHub Actions (CI/CD) e Vercel (hosting).

### 1. GitHub Actions Secrets

I **GitHub Secrets** sono variabili d'ambiente crittografate che vengono utilizzate nei workflow CI/CD senza esporre valori sensibili nel codice.

```bash
# Esporta le variabili (usa i tuoi valori reali)
export DATABASE_URL="**************************************************/rebuildlink"
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_ANON_KEY="eyJhbGciOi..."

# Salvale nel repo GitHub tramite script automatizzato
./scripts/set_github_secrets.sh
```

**Cosa fa lo script:**

1. **Valida prerequisiti**: Controlla che GitHub CLI sia installato e autenticato
2. **Verifica variabili**: Assicura che tutte le variabili richieste siano esportate
3. **Identifica repository**: Rileva automaticamente il repo GitHub corrente
4. **Salva secrets**: Utilizza `gh secret set` per crittografare e salvare i valori
5. **Conferma successo**: Mostra un riepilogo delle operazioni completate

### 2. Configurazione Vercel

Per il deployment su Vercel, le variabili d'ambiente devono essere configurate nella dashboard del progetto.

```bash
# Copia il template e personalizzalo
cp vercel.env.example vercel.env.local

# Modifica il file con i tuoi valori reali
# Poi importa le variabili nella dashboard Vercel
```

**Metodi di configurazione:**

**A) Dashboard Vercel (Raccomandato):**

1. Vai su [vercel.com](https://vercel.com) > Il tuo progetto
2. Settings > Environment Variables
3. Aggiungi manualmente ogni variabile:
   - `DATABASE_URL` → Il tuo endpoint RDS
   - `SUPABASE_URL` → URL del progetto Supabase
   - `SUPABASE_ANON_KEY` → Chiave pubblica Supabase

**B) Vercel CLI:**

```bash
# Installa Vercel CLI
npm i -g vercel

# Login e deploy
vercel login
vercel --prod
```

### 3. Variabili Richieste

| Variabile                   | Descrizione                | Dove Ottenerla                      | Sicurezza   |
| --------------------------- | -------------------------- | ----------------------------------- | ----------- |
| `DATABASE_URL`              | Connessione PostgreSQL RDS | `./scripts/check_rds_status.sh`     | 🔒 Segreta  |
| `SUPABASE_URL`              | URL progetto Supabase      | Dashboard Supabase > Settings > API | 🌐 Pubblica |
| `SUPABASE_ANON_KEY`         | Chiave anonima Supabase    | Dashboard Supabase > Settings > API | 🌐 Pubblica |
| `SUPABASE_SERVICE_ROLE_KEY` | Chiave service role        | Dashboard Supabase > Settings > API | 🔒 Segreta  |

### 4. Best Practices per la Sicurezza

- ✅ **Mai committare** file `.env` con valori reali
- ✅ **Usa prefisso** `NEXT_PUBLIC_` solo per variabili sicure da esporre
- ✅ **Mantieni sincronizzati** GitHub Secrets e Vercel Environment Variables
- ✅ **Testa sempre** dopo aver aggiornato le variabili
- ✅ **Ruota periodicamente** le chiavi sensibili (DATABASE_URL, SERVICE_ROLE_KEY)

### 5. Troubleshooting Comune

**Problema**: App non si connette al database

```bash
# Verifica che DATABASE_URL sia corretta
./scripts/check_rds_status.sh
```

**Problema**: Autenticazione Supabase non funziona

```bash
# Controlla che SUPABASE_URL e SUPABASE_ANON_KEY siano corrette
# Dashboard Supabase > Settings > API
```

**Problema**: API server-side falliscono

```bash
# Verifica SUPABASE_SERVICE_ROLE_KEY
# Assicurati che sia configurata solo come secret (non NEXT_PUBLIC_)
```

💡 **Suggerimento didattico**: Mantieni sempre sincronizzati i secrets tra GitHub (per i workflow CI) e Vercel (per l'app in esecuzione) per evitare incongruenze di ambiente.

---

## ⚙️ CI/CD con GitHub Actions

> In questa sezione implementiamo una pipeline completa di Continuous Integration
> e Continuous Deployment che automatizza test, build e deploy ad ogni commit.

### 1. Workflow Automatizzato

Il file `.github/workflows/ci.yml` definisce una pipeline che si attiva automaticamente:

**Trigger:**

- ✅ **Push su `main`** → Deploy in produzione
- ✅ **Pull Request** → Deploy preview per testing

**Fasi del workflow:**

1. **Setup** → Prepara ambiente Node.js 20 con cache npm
2. **Quality** → Esegue lint (ESLint) e test (Jest)
3. **Build** → Compila versione produzione Next.js
4. **Deploy** → Pubblica su Vercel (prod o preview)
5. **Notify** → Conferma successo o segnala errori

### 2. Configurazione Secrets Vercel

Oltre ai secrets base, il workflow richiede credenziali Vercel:

```bash
# Configura automaticamente i secrets Vercel
./scripts/setup_vercel_secrets.sh
```

**Secrets aggiuntivi configurati:**

- `VERCEL_TOKEN` → Token personale per autenticazione
- `VERCEL_ORG_ID` → ID organizzazione Vercel
- `VERCEL_PROJECT_ID` → ID progetto specifico

### 3. Flusso di Sviluppo

**Sviluppo locale:**

```bash
# 1. Crea feature branch
git checkout -b feature/nuova-funzionalita

# 2. Sviluppa e testa localmente
npm run dev
npm run test
npm run lint

# 3. Commit e push
git add .
git commit -m "feat: aggiunge nuova funzionalità"
git push origin feature/nuova-funzionalita
```

**Review e Deploy:**

```bash
# 4. Crea Pull Request su GitHub
# → Trigger automatico: lint + test + build + deploy preview

# 5. Review del codice e merge su main
# → Trigger automatico: deploy produzione
```

### 4. Monitoraggio Pipeline

**Dashboard GitHub Actions:**

- Vai su `https://github.com/TUO-USERNAME/REPO/actions`
- Monitora stato di ogni workflow
- Visualizza log dettagliati per debugging

**Notifiche automatiche:**

- ✅ **Successo**: URL deploy + dettagli commit
- ❌ **Fallimento**: Log errori + suggerimenti risoluzione

### 5. Vantaggi della Pipeline

- ✅ **Qualità garantita**: Nessun deploy senza test passati
- ✅ **Deploy automatico**: Zero intervento manuale
- ✅ **Preview sicure**: Test su URL temporanei prima della produzione
- ✅ **Rollback rapido**: Cronologia deploy per ripristini veloci
- ✅ **Feedback immediato**: Notifiche in tempo reale su stato deploy

### 6. Troubleshooting CI/CD

**Workflow fallisce al lint:**

```bash
# Risolvi errori localmente
npm run lint -- --fix
git add . && git commit -m "fix: risolve errori lint"
```

**Test non passano:**

```bash
# Esegui test localmente per debug
npm run test -- --verbose
# Correggi e ricommit
```

**Deploy Vercel fallisce:**

```bash
# Verifica secrets configurati
gh secret list --repo TUO-USERNAME/REPO
# Ricontrolla configurazione Vercel
./scripts/setup_vercel_secrets.sh
```

💡 **Suggerimento didattico**: La pipeline CI/CD trasforma ogni commit in un potenziale release, forzando buone pratiche di sviluppo e garantendo qualità costante del codice in produzione.

### 7. Automigrazioni Database

Il workflow CI esegue automaticamente tutti i file `migrations/*.sql` contro l'istanza RDS **prima** del deploy, garantendo che lo schema sia sempre allineato con il codice applicativo.

**Sistema di tracking intelligente:**

- ✅ **Tabella `schema_migrations`**: Traccia migrazioni già applicate
- ✅ **Checksum verification**: Rileva modifiche ai file esistenti
- ✅ **Transazioni atomiche**: Rollback automatico in caso di errore
- ✅ **Ordinamento automatico**: Esecuzione in ordine alfabetico/cronologico

**Workflow migrazioni:**

```bash
# 1. Aggiungi nuovo file SQL con naming convention
echo "ALTER TABLE users ADD COLUMN bio TEXT;" > migrations/$(date +%Y%m%d)_add_user_bio.sql

# 2. Commit e push
git add migrations/$(date +%Y%m%d)_add_user_bio.sql
git commit -m "feat(db): add bio column to users"
git push

# → GitHub Actions applicherà automaticamente la migrazione
```

**Naming convention raccomandata:**

```
migrations/
├── 001_initial_schema.sql          # Schema iniziale
├── 002_add_categories.sql           # Aggiunta tabelle
├── 20250109_add_user_bio.sql        # Con timestamp
├── 20250110_create_indexes.sql      # Ottimizzazioni
└── 20250115_add_notifications.sql   # Nuove funzionalità
```

**Gestione locale:**

```bash
# Esegui migrazioni localmente
export DATABASE_URL="postgres://user:pass@localhost:5432/rebuildlink"
./scripts/run_migrations.sh

# Verifica stato migrazioni
psql "$DATABASE_URL" -c "SELECT filename, applied_at FROM schema_migrations ORDER BY applied_at;"
```

**Vantaggi del sistema:**

- ✅ **Versionamento schema**: Evoluzione database tracciata nel git
- ✅ **Riproducibilità**: Stesso schema in dev, staging, produzione
- ✅ **Zero downtime**: Migrazioni non bloccanti quando possibile
- ✅ **Rollback sicuro**: Cronologia completa per ripristini
- ✅ **Team sync**: Tutti i developer hanno stesso schema

💡 **Tip didattico**: Mantenere le migrazioni versionate nel repo permette di tracciare l'evoluzione dello schema e di riprodurre lo stato DB in ogni ambiente (dev, staging, prod) in modo deterministico.

---

**Let’s rebuild, together.**

## ⛳ Bootstrap Completo (Da Zero a Cloud)

> Script finale che automatizza completamente il setup del progetto:
> da cartella locale a produzione cloud con un solo comando.

### 1. Setup Automatizzato Completo

```bash
# Esporta le variabili richieste
export DATABASE_URL="******************************/db"
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_ANON_KEY="eyJhbGciOi..."

# (Opzionale) Personalizza nomi progetto
export GH_REPO_NAME="my-awesome-app"
export VERCEL_PROJECT_NAME="my-awesome-app"
export GH_PRIVATE=false  # Per repository pubblico

# Avvia bootstrap completo!
./scripts/bootstrap_cli_project.sh
```

### 2. Cosa Fa lo Script Bootstrap

**Fase 1 - Validazione:**

- ✅ Controlla prerequisiti (git, gh, vercel)
- ✅ Verifica autenticazione GitHub e Vercel
- ✅ Valida variabili d'ambiente richieste

**Fase 2 - Repository GitHub:**

- ✅ Crea repository GitHub (pubblico/privato)
- ✅ Inizializza Git se necessario
- ✅ Effettua push iniziale del codice

**Fase 3 - GitHub Actions:**

- ✅ Configura tutti i secrets necessari
- ✅ Attiva pipeline CI/CD automatica

**Fase 4 - Progetto Vercel:**

- ✅ Crea o collega progetto Vercel
- ✅ Configura framework Next.js
- ✅ Gestisce team/organizzazioni

**Fase 5 - Variabili Vercel:**

- ✅ Configura variabili per production
- ✅ Configura variabili per preview
- ✅ Sincronizza con GitHub secrets

**Fase 6 - CI/CD Integration:**

- ✅ Configura token Vercel per deploy automatico
- ✅ Attiva pipeline completa

### 3. Prerequisiti

**Software richiesto:**

```bash
# Git (version control)
git --version

# GitHub CLI (repository management)
gh auth login

# Vercel CLI (deployment)
npm i -g vercel
vercel login
```

**Variabili d'ambiente:**

- `DATABASE_URL` → Endpoint RDS PostgreSQL
- `SUPABASE_URL` → URL progetto Supabase
- `SUPABASE_ANON_KEY` → Chiave pubblica Supabase

### 4. Personalizzazione

**Variabili opzionali:**

```bash
export GH_REPO_NAME="rebuild-link-marketplace"    # Nome repository
export GH_PRIVATE=true                            # Repository privato
export VERCEL_PROJECT_NAME="rebuild-link"         # Nome progetto Vercel
export VERCEL_TEAM="my-team"                      # Team Vercel (se applicabile)
```

### 5. Risultato Finale

Dopo l'esecuzione avrai:

- 📦 **Repository GitHub** con codice e CI/CD attivo
- 🔑 **Secrets configurati** per GitHub Actions
- 🚀 **Progetto Vercel** pronto per deploy
- 🌍 **Variabili d'ambiente** sincronizzate
- ⚙️ **Pipeline automatica** da commit a produzione

### 6. Test della Pipeline

```bash
# Fai una modifica qualsiasi
echo "# Test" >> README.md

# Commit e push per attivare CI/CD
git add .
git commit -m "feat: test automated pipeline"
git push origin main

# → Automaticamente:
#   • Lint + Test + Build
#   • Automigrazioni database
#   • Deploy produzione Vercel
```

### 7. Monitoraggio

**GitHub Actions:**

- URL: `https://github.com/TUO-USERNAME/REPO/actions`
- Monitora: Lint, test, build, migrazioni, deploy

**Vercel Dashboard:**

- URL: `https://vercel.com/dashboard`
- Monitora: Deploy, performance, domini

### 8. Vantaggi del Bootstrap

- ✅ **Zero configurazione manuale**: Tutto automatizzato
- ✅ **Best practices integrate**: Configurazione professionale
- ✅ **Errore-proof**: Validazioni e controlli integrati
- ✅ **Riproducibile**: Stesso setup per ogni progetto
- ✅ **Didattico**: Ogni step spiegato e commentato

💡 **Suggerimento finale**: Una volta eseguito il bootstrap, ogni commit diventa un potenziale deploy. La pipeline completa garantisce qualità e sicurezza ad ogni rilascio!
